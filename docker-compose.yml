version: '3.8'
services:
  mcp-server:
    build:
      context: .
      dockerfile: ./apps/mcp-server/Dockerfile
    ports:
      - "3010:4333"
    depends_on:
      - redis
      - db
    env_file:
      - .env

  redis:
    image: "redis:8.2.0-alpine"
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  db:
    image: postgres:17.5-alpine
    restart: always
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    env_file:
      - .env

volumes:
  redis_data:
  postgres_data:
