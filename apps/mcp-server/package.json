{"name": "mcp-server", "version": "0.1.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "bin": {"mcp-server": "dist/index.js"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "type": "module", "repository": {"type": "git", "url": "git+https://github.com/limcheekin/project-concord.git"}, "bugs": {"url": "https://github.com/limcheekin/project-concord/issues"}, "homepage": "https://github.com/limcheekin/project-concord#readme", "scripts": {"build": "tsc", "postbuild": "tsx scripts/make-executable.ts dist/index.js", "start": "node dist/index.js", "start:stdio": "MCP_LOG_LEVEL=debug MCP_TRANSPORT_TYPE=stdio node dist/index.js", "start:http": "MCP_LOG_LEVEL=debug MCP_TRANSPORT_TYPE=http node dist/index.js", "start:agent": "MCP_LOG_LEVEL=debug node dist/agent/cli/boot.js", "dev": "tsx --watch src/index.ts", "dev:stdio": "MCP_LOG_LEVEL=debug MCP_TRANSPORT_TYPE=stdio tsx --watch src/index.ts", "dev:http": "MCP_LOG_LEVEL=debug MCP_TRANSPORT_TYPE=http tsx --watch src/index.ts", "dev:agent": "MCP_LOG_LEVEL=debug tsx --watch src/agent/cli/boot.ts", "rebuild": "tsx scripts/clean.ts && npm run build", "docs:generate": "typedoc --tsconfig ./tsconfig.typedoc.json", "depcheck": "npx depcheck", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "tree": "tsx scripts/tree.ts", "fetch-spec": "tsx scripts/fetch-openapi-spec.ts", "seed-test-db": "tsx scripts/seed-test-db.ts", "format": "prettier --write \"**/*.{ts,js,json,md,html,css}\"", "inspector": "npx mcp-inspector --config mcp.json --server mcp-server", "db:duckdb-example": "MCP_LOG_LEVEL=debug tsc && node dist/storage/duckdbExample.js", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "audit": "npm audit --production", "audit:fix": "npm audit fix --production", "prepublishOnly": "npm run typecheck && npm run lint && npm run test && npm run build"}, "dependencies": {"@duckdb/node-api": "^1.3.2-alpha.25", "@hono/node-server": "^1.17.1", "@modelcontextprotocol/sdk": "^1.17.1", "@opentelemetry/instrumentation-winston": "^0.48.0", "@supabase/supabase-js": "^2.53.0", "axios": "^1.11.0", "chrono-node": "^2.8.0", "dotenv": "^16.6.1", "hono": "^4.8.9", "ignore": "^7.0.5", "jose": "^6.0.12", "js-yaml": "^4.1.0", "node-cron": "^4.2.1", "openai": "^5.11.0", "partial-json": "^0.1.7", "reflect-metadata": "^0.2.2", "sanitize-html": "^2.17.0", "tiktoken": "^1.0.21", "validator": "13.15.15", "winston": "^3.17.0", "winston-transport": "^4.9.0", "zod": "^4.0.17", "ioredis": "^5.4.1", "pg": "^8.16.3", "data-contract": "file:../../packages/data-contract", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/exporter-metrics-otlp-http": "^0.203.0", "@opentelemetry/exporter-trace-otlp-http": "^0.203.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-metrics": "^2.0.1", "@opentelemetry/sdk-node": "^0.203.0", "@opentelemetry/sdk-trace-node": "^2.0.1", "@opentelemetry/semantic-conventions": "^1.36.0"}, "devDependencies": {"@anatine/zod-mock": "^3.14.0", "@eslint/js": "^9.32.0", "@faker-js/faker": "^9.9.0", "@types/js-yaml": "^4.0.9", "@types/node": "^24.1.0", "@types/node-cron": "^3.0.11", "@types/sanitize-html": "^2.16.0", "@types/validator": "13.15.2", "@vitest/coverage-v8": "^3.2.4", "depcheck": "^1.4.7", "eslint": "^9.32.0", "globals": "^16.3.0", "msw": "^2.10.4", "prettier": "^3.6.2", "tsx": "^4.20.3", "typedoc": "^0.28.8", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "@types/pg": "^8.15.5", "typescript-config-custom": "file:../../packages/typescript-config"}, "keywords": ["typescript", "mcp", "model-context-protocol", "agent", "autonomous-agent", "agent-framework", "architecture", "error-handling", "llm", "ai-integration", "mcp-server", "mcp-client", "hono", "stdio", "http", "authentication", "o<PERSON>h", "jwt", "openrouter", "duckdb", "postgresql", "redis", "zod", "opentelemetry", "observability", "tracing", "metrics"], "author": "limcheekin <<EMAIL>> (https://github.com/limcheekin/project-concord#readme)", "license": "BSL-1.1", "engines": {"node": ">=20.0.0"}}