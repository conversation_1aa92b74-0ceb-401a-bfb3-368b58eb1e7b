import { describe, it, expect } from 'vitest';
import { buildQueryFromDescriptionLogic } from '../../../src/mcp-server/tools/build_query_from_description/logic';
import { McpError } from '../../../src/utils/internal/mcpError';
import { BaseErrorCode } from '../../../src/types-global/BaseErrorCode';

describe('buildQueryFromDescriptionLogic', () => {
  const mockContract = {
    tables: {},
    abbreviations: {},
    tools: {},
  };

  it('should return a SQL query for a supported description', async () => {
    const input = { description: 'Show me the full name for the customer with ID 123.' };
    const result = await buildQueryFromDescriptionLogic(mockContract, input);
    expect(result).toEqual({ sql_query: 'SELECT c_name FROM CUST_MST WHERE c_id = 123;' });
  });

  it('should throw an error for an unsupported description', async () => {
    const input = { description: 'This is not a supported query.' };
    await expect(buildQueryFromDescriptionLogic(mockContract, input)).rejects.toThrow(McpError);
    await expect(buildQueryFromDescriptionLogic(mockContract, input)).rejects.toHaveProperty('code', BaseErrorCode.UNSUPPORTED_OPERATION);
  });
});
