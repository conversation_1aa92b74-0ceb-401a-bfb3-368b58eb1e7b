import { Pool } from 'pg';
import { config } from "../config/index.js";

/**
 * A connection pool for the PostgreSQL database.
 * It reads the connection configuration from the DATABASE_URL environment variable.
 *
 * @see https://node-postgres.com/features/connecting#connection-uri
 */
export const pool = new Pool({
  connectionString: config.databaseUrl,
});

pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});
