export enum BaseErrorCode {
    // General Errors
    UNKNOWN_ERROR = 'UNKNOWN_ERROR',
    INVALID_INPUT = 'INVALID_INPUT',
    UNAUTHORIZED = 'UNAUTHORIZED',
    FORBIDDEN = 'FORBIDDEN',
    NOT_FOUND = 'NOT_FOUND',
    INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
    BAD_GATEWAY = 'BAD_GATEWAY',
    SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
    GATEWAY_TIMEOUT = 'GATEWAY_TIMEOUT',

    // MCP Specific Errors
    UNSUPPORTED_OPERATION = 'UNSUPPORTED_OPERATION',
    INVALID_SESSION = 'INVALID_SESSION',
    SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',
    INVALID_TRANSPORT = 'INVALID_TRANSPORT',
  }
