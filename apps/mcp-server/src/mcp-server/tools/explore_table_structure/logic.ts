import { z } from 'zod';
import { McpError } from '../../../utils/internal/mcpError.js';
import { BaseErrorCode } from '../../../types-global/BaseErrorCode.js';
import { DataContract } from 'data-contract';

export const exploreTableStructureInputSchema = z.object({
  tableName: z.string(),
});

// Define a proper schema for the table structure output
export const exploreTableStructureOutputSchema = z.object({
  tableName: z.string(),
  columns: z.array(z.object({
    name: z.string(),
    type: z.string(),
    nullable: z.boolean().optional(),
    description: z.string().optional(),
  })).optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

export async function exploreTableStructureLogic(
  contract: DataContract,
  input: z.infer<typeof exploreTableStructureInputSchema>
): Promise<any> {
  const table = contract.tables[input.tableName];
  if (!table) {
    throw new McpError(BaseErrorCode.NOT_FOUND, `Table not found: ${input.tableName}`);
  }
  // Return a copy to avoid callers modifying the original contract object
  return { ...table };
}
