import { z } from 'zod';
import { McpError } from '../../../utils/internal/mcpError.js';
import { BaseErrorCode } from '../../../types-global/BaseErrorCode.js';
import { DataContract } from 'data-contract';

export const explainColumnMeaningInputSchema = z.object({
  tableName: z.string(),
  columnName: z.string(),
});

export const explainColumnMeaningOutputSchema = z.object({
  businessName: z.string(),
  description: z.string(),
  businessRules: z.array(z.string()),
});

export async function explainColumnMeaningLogic(
  contract: DataContract,
  input: z.infer<typeof explainColumnMeaningInputSchema>
): Promise<z.infer<typeof explainColumnMeaningOutputSchema>> {
  const table = contract.tables[input.tableName];
  if (!table) {
    throw new McpError(BaseErrorCode.NOT_FOUND, `Table not found: ${input.tableName}`);
  }

  const column = table.columns[input.columnName];
  if (!column) {
    throw new McpError(BaseErrorCode.NOT_FOUND, `Column not found: ${input.columnName} in table ${input.tableName}`);
  }

  const { businessName, description, businessRules } = column;
  // Return a new object with the relevant fields
  return { businessName, description, businessRules: businessRules || [] };
}
