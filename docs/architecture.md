# **Project Concord: Fullstack Architecture Document**

### **1. Introduction**

This document outlines the complete full-stack architecture for Project Concord, including the backend service, its data contract management, and integration points. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

**Starter Template or Existing Project**

The PRD does not specify an existing project or starter template. Based on the requirement for a greenfield TypeScript monorepo, we will use **Turborepo** as our foundational build system and monorepo manager. This provides the benefits of a starter template (fast setup, best practices) while giving us full control over the architecture.

**Change Log**

| Date | Version | Description | Author |
| :--- | :--- | :--- | :--- |
| 2025-08-04 | 1.0 | Initial architecture draft created and finalized. | <PERSON> (Architect) |

-----

### **2. High Level Architecture**

**Technical Summary**

The architecture for Project Concord will be a **headless, containerized MCP server** developed in TypeScript. It will function as a **Custom Metadata-Enriching Server**, acting as a secure and observable semantic mediation layer between our legacy database and authorized AI agents. The system will be built within a **Turborepo monorepo** to manage the server code and shared packages, such as the Data Contract definitions. The initial MVP will be strictly **read-only** to mitigate risk.

**Platform and Infrastructure Choice**

  * **Platform**: The system will be platform-agnostic, designed for deployment in any environment that supports **Docker containers**.
  * **Key Services**:
      * **Compute**: A container orchestration service (e.g., AWS ECS, Azure Container Apps, or Kubernetes).
      * **Caching**: A managed **Redis** service for performance optimization.
      * **Logging**: A centralized logging platform (e.g., AWS CloudWatch, Datadog) to aggregate the server's structured logs for observability.

**Repository Structure**

  * **Structure**: **Monorepo**, managed by Turborepo.
  * **Package Organization**: The monorepo will contain:
      * An `apps/mcp-server` package for the main server application.
      * A `packages/data-contract` package for the version-controlled `datacontract.yml` and its validation scripts.
      * A `packages/eslint-config` and `packages/typescript-config` for shared development configurations.

**High Level Architecture Diagram**

```mermaid
graph TD
    subgraph "AI Agent Environment"
        A[AI Agent / IDE]
    end

    subgraph "Project Concord Service"
        B[MCP Server <br> (TypeScript, Docker Container)]
        C[Data Contract <br> (datacontract.yml)]
        D[Redis Cache]
    end

    subgraph "Legacy Environment"
        E[Legacy Database]
    end

    A -- "MCP (JSON-RPC)" --> B
    B -- "Reads" --> C
    B -- "Caches Mappings" --> D
    B -- "Reads" --> D
    B -- "Safe Read-Only SQL" --> E
```

**Architectural Patterns**

  * **Custom Metadata-Enriching Server**: We are building the mediation logic ourselves for maximum control, rather than using a pre-built federated engine.
  * [cite\_start]**Adapter Pattern**: The server acts as an adapter, converting the legacy database's implicit, cryptic schema into the explicit, standardized MCP interface[cite: 343].
  * [cite\_start]**Facade Pattern**: The server provides a single, simplified interface (the MCP tools) for the complex operations of auditing, mapping, and safely querying the legacy database[cite: 341].

-----

### **3. Tech Stack**

This section provides the definitive list of technologies, libraries, and tools that will be used to build and operate Project Concord.

| Category | Technology | Version | Purpose | Rationale |
| :--- | :--- | :--- | :--- | :--- |
| **Language** | TypeScript | \~5.4 | Primary development language | Provides type safety and scalability, ideal for a production system. |
| **Runtime** | Node.js | \~20.x (LTS)| JavaScript runtime environment | LTS version ensures stability and long-term support. |
| **Monorepo Manager**| Turborepo | \~2.0 | Build system and monorepo manager | High-performance build caching to accelerate development and CI/CD. |
| **Web Framework** | Hono | \~4.5 | Core server framework | Lightweight, fast, and well-suited for a headless MCP service. |
| **MCP SDK** | @modelcontextprotocol/sdk | \~1.1 | MCP server implementation | Official SDK for adhering to the MCP specification. |
| **Containerization**| Docker | Latest | Environment consistency | Ensures development, testing, and production environments are identical. |
| **Testing** | Vitest | \~3.2.4 | Unit & Integration testing | Modern, fast testing framework compatible with our stack. |
| **Linting** | ESLint | \~9.5 | Code quality and consistency | Enforces a consistent coding style across the monorepo. |
| **Formatting** | Prettier | \~3.3 | Automated code formatting | Works with ESLint to maintain a clean and readable codebase. |
| **Caching** | Redis | \~7.2 | In-memory cache for mappings | High-performance caching to reduce latency on repeated schema lookups. |
| **CI/CD** | GitHub Actions| N/A | Continuous integration & deployment | Automates testing and deployment workflows directly from the repository. |

-----

### **4. Data Models**

The server will dynamically discover the legacy schema during the **Schema Debt Audit** (Story 1.2) and use the resulting **Data Contract** (Story 1.3) to define its models at runtime. Clean, enriched TypeScript interfaces corresponding to the Data Contract will be stored in the shared `packages/data-contract` to ensure type safety, complete with JSDoc comments for context.

-----

### **5. API Specification**

The server's API will strictly adhere to the **Model Context Protocol (MCP)**, which uses JSON-RPC 2.0. The core of the specification is the definition of the **Tools** the server exposes to the AI agent. These tool definitions, including their **Input Validation Schemas**, will be a formal part of our `datacontract.yml`. The server will also provide a **Standardized Error Response Format** for validation failures.

-----

### **6. Core Workflows**

The primary workflow is the **Schema Exploration Workflow**, which allows an AI agent to safely query the server to understand the legacy schema. The server validates the request, checks its Redis cache for the semantic mapping, and if not present, reads from the Data Contract to enrich the raw schema details before returning the response to the agent.

-----

### **7. Database Schema**

Project Concord does not define its own database schema for application data. Its architectural approach is as follows:

1.  **Dynamic Discovery**: Discover the legacy schema via the **Schema Debt Audit** (Story 1.2).
2.  **Schema Caching**: Cache the discovered raw schema locally in a file to ensure fast startups and reduce load on the legacy system.
3.  **Semantic Layering**: Map the cached schema to the business-friendly definitions within the **Data Contract**.
4.  **No Direct Modification**: Enforce a strict read-only interaction policy with the legacy database for the MVP.

-----

### **8. Unified Project Structure**

The project will use a Turborepo-managed monorepo structure to promote code sharing and maintainability.

```plaintext
project-concord/
├── apps/
│   └── mcp-server/         # The primary, headless MCP server application
├── packages/
│   ├── data-contract/      # The single source of truth for our schema
│   │   ├── datacontract.yml
│   │   └── src/types.ts
│   ├── eslint-config/      # Shared ESLint configuration
│   └── typescript-config/  # Shared tsconfig.json settings
├── .github/
│   └── workflows/          # GitHub Actions for CI/CD
└── package.json            # Root package.json
```

-----

### **9. Development Workflow**

The local development process will be managed via npm scripts in the root `package.json` and documented in the "Developer Onboarding Guide" (Story 1.1). The process includes cloning the repository, installing dependencies with `npm install`, configuring a local `.env` file, and using `docker-compose` to run local instances of the test database and Redis.

-----

### **10. Deployment Architecture**

The server will be deployed as a **Docker container**. A **GitHub Actions CI/CD pipeline** will be used to automatically test, build, and push a versioned container image to a registry. Deployments to Staging will be automatic on merge to `main`, while Production deployments will require a manual trigger.

-----

### **11. Security and Performance**

**Security Requirements**

  * **Input Validation**: All incoming AI agent requests will be validated against a strict JSON Schema.
  * **Credential Management**: Database credentials will be loaded from environment variables, injected from a secrets management service in production.
  * **Least Privilege Principle**: The database user account for the server will have read-only permissions.
  * **Dependency Security**: The CI/CD pipeline will include a step to scan for known vulnerabilities in all third-party npm packages.

**Performance Optimization**

  * **Schema Caching**: The discovered raw legacy schema will be cached locally to ensure fast server startups.
  * **Semantic Mapping Cache**: The entire **Data Contract** will be loaded into **Redis** on server startup. This provides the lowest latency for all subsequent tool executions that depend on semantic mappings, as it avoids repeated file system access.
  * **Connection Pooling**: The server will use a connection pool to manage connections to the legacy database efficiently.
  * **Query Analysis**: Observability logs will be used to identify and analyze slow or inefficient SQL queries generated by the AI.

-----

### **12. Testing Strategy**

The project will adhere to the "Full Testing Pyramid" requirement from the PRD, implemented with a multi-layered strategy:

  * **Unit Tests**: Using Vitest to test individual functions and classes in isolation, mocking all external dependencies.
  * **Integration Tests**: Using Vitest with the containerized test database to verify the interactions between the server's internal components.
  * **End-to-End (E2E) Tests**: Using a test script to validate the server's logic deterministically (Layer 1) and then to benchmark performance with a real AI agent (Layer 2).