"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseDataContract = parseDataContract;
exports.getDataContractTable = getDataContractTable;
exports.generateMarkdown = generateMarkdown;
const yaml = __importStar(require("js-yaml"));
/**
 * Parses the YAML content of a data contract.
 * This is a pure function with no file I/O.
 * @param yamlContent The string content of the datacontract.yml file.
 * @returns The parsed data contract object.
 */
function parseDataContract(yamlContent) {
    return yaml.load(yamlContent);
}
/**
 * Gets the data for a specific table from the data contract.
 * @param contract The parsed data contract.
 * @param tableName The name of the table to retrieve.
 * @returns The table data, or undefined if the table is not found.
 */
function getDataContractTable(contract, tableName) {
    return contract.tables[tableName];
}
/**
 * Generates Markdown documentation for a given table.
 * @param tableData The data for the table.
 * @returns A Markdown string.
 */
function generateMarkdown(tableData) {
    const { businessName, description, columns } = tableData;
    let markdownContent = `# ${businessName}\n\n`;
    markdownContent += `${description}\n\n`;
    markdownContent += `## Columns\n\n`;
    markdownContent += `| Business Name | Description | Data Type | Business Rules |\n`;
    markdownContent += `|---|---|---|---|\n`;
    for (const columnName in columns) {
        const column = columns[columnName];
        const businessRules = column.businessRules ? column.businessRules.join(', ') : '';
        markdownContent += `| ${column.businessName} | ${column.description} | ${column.dataType} | ${businessRules} |\n`;
    }
    return markdownContent;
}
